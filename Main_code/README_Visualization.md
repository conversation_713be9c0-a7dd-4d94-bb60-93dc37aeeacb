# Module de Visualisation des Parkings

Ce module ajoute des capacités de visualisation avancées au projet d'optimisation de parkings, permettant de comparer visuellement les layouts avant et après optimisation.

## 🎯 Fonctionnalités

### 1. **Comparaison Avant/Après**
- Affichage côte à côte du parking initial et optimisé
- Calcul automatique des statistiques d'amélioration
- Légende claire avec codes couleur
- Marquage des entrées et sorties

### 2. **Évolution Progressive**
- Visualisation de l'évolution sur plusieurs générations
- Grille montrant les étapes clés de l'optimisation
- Scores associés à chaque génération

### 3. **Visualisation Détaillée**
- Affichage individuel avec grille et annotations
- Statistiques détaillées (pourcentages, efficacité)
- Marquage spécial des entrées/sorties

## 🎨 Code Couleur

| Élément | Couleur | Code |
|---------|---------|------|
| **Murs/Obstacles** | 🔵 Bleu foncé | `NUM_MUR = -1` |
| **Routes** | ⚪ Gris clair | `NUM_ROUTE = 0` |
| **Places de parking** | 🟢 Vert | `NUM_PLACE = 1` |
| **Voitures** | 🔴 Rouge | `NUM_VOITURE = 2` |
| **Entrée** | 🔵 Bordure bleue | Marquage spécial |
| **Sortie** | 🟠 Bordure orange | Marquage spécial |

## 📁 Fichiers du Module

### `parking_visualizer.py`
Module principal contenant:
- `ParkingVisualizer`: Classe principale de visualisation
- `visualize_parking_optimization()`: Fonction de haut niveau
- `create_test_parking()`: Utilitaire pour créer des parkings de test

### `demo_visualization.py`
Script de démonstration montrant toutes les capacités:
- Création de parkings de démonstration
- Évolution simulée
- Génération de tous les types de visualisations

## 🚀 Utilisation

### Utilisation Automatique
Le module est automatiquement intégré dans `programme_principal.py`:

```python
# Lancer l'optimisation avec visualisation automatique
python programme_principal.py
```

**Fichiers générés:**
- `parking_comparison.png` - Comparaison avant/après
- `parking_evolution.png` - Évolution sur plusieurs générations
- `evolution_scores.png` - Graphique des scores
- `score_distribution.png` - Distribution des scores

### Utilisation Manuelle

```python
from parking_visualizer import ParkingVisualizer, visualize_parking_optimization

# Créer le visualiseur
visualizer = ParkingVisualizer()

# Comparaison simple
visualizer.plot_comparison(
    initial_parking, 
    final_parking, 
    initial_score, 
    final_score,
    save_path="my_comparison.png"
)

# Évolution complète
visualizer.plot_evolution_grid(
    parking_evolution, 
    scores_evolution,
    save_path="my_evolution.png"
)
```

### Démonstration Complète

```python
# Voir toutes les capacités de visualisation
python demo_visualization.py
```

**Fichiers générés:**
- `demo_comparison.png` - Comparaison avec parkings optimisés
- `demo_evolution.png` - Évolution progressive simulée
- `demo_detailed.png` - Vue détaillée avec statistiques

## 📊 Types de Visualisations

### 1. **Comparaison Côte à Côte**
```python
visualizer.plot_comparison(initial, final, score_init, score_final)
```
- Deux parkings affichés côte à côte
- Statistiques d'amélioration en haut
- Légende commune en bas
- Sauvegarde automatique en haute résolution

### 2. **Grille d'Évolution**
```python
visualizer.plot_evolution_grid(evolution_array, scores_array)
```
- Affichage de 6 générations clés par défaut
- Scores affichés sous chaque parking
- Disposition automatique en grille
- Générations personnalisables

### 3. **Parking Individuel**
```python
visualizer.plot_single_parking(parking, title="Mon Parking")
```
- Affichage détaillé d'un seul parking
- Grille optionnelle pour précision
- Marquage des entrées/sorties
- Titre personnalisable

## 🔧 Configuration

### Personnalisation des Couleurs
```python
visualizer = ParkingVisualizer()
visualizer.colors[NUM_PLACE] = '#FF5733'  # Changer couleur des places
```

### Paramètres d'Export
```python
plt.savefig('parking.png', 
           dpi=300,           # Haute résolution
           bbox_inches='tight', # Ajustement automatique
           facecolor='white')   # Fond blanc
```

## 📈 Statistiques Affichées

### Métriques Automatiques
- **Amélioration du score**: Différence entre final et initial
- **Nombre de places**: Comptage des cellules de parking
- **Nombre de routes**: Comptage des cellules de route
- **Efficacité**: Pourcentage d'espace utilisé pour le parking
- **Densité**: Ratio places/routes

### Exemple de Sortie
```
AMÉLIORATION: +102.7 points
Places initiales: 23 → Places finales: 63
Routes initiales: 45 → Routes finales: 67
Efficacité: 28.0% de l'espace utilisé pour le parking
```

## 🎯 Intégration avec l'Algorithme Génétique

Le module s'intègre parfaitement avec l'algorithme génétique existant:

1. **Capture automatique** du parking initial
2. **Suivi de l'évolution** à chaque génération
3. **Visualisation finale** automatique
4. **Sauvegarde** de tous les résultats

## 🔍 Exemples de Résultats

### Parking Mal Optimisé
- Score: -8.5
- Places: 23 (10.2%)
- Routes fragmentées
- Pas de connexion entrée-sortie

### Parking Bien Optimisé  
- Score: 94.2
- Places: 63 (28.0%)
- Routes organisées en grille
- Connexion claire entrée-sortie

## 🛠️ Dépendances

- `numpy`: Manipulation des arrays de parkings
- `matplotlib`: Génération des graphiques
- `matplotlib.patches`: Annotations et marquages

## 📝 Notes Techniques

- **Backend non-interactif**: Utilise 'Agg' pour éviter les fenêtres popup
- **Haute résolution**: Images sauvegardées en 300 DPI
- **Mémoire optimisée**: Fermeture automatique des figures
- **Compatible**: Fonctionne avec tous les environnements Python

## 🎉 Résultats

Le module de visualisation transforme les données numériques de l'algorithme génétique en représentations visuelles claires et informatives, facilitant:

- **L'analyse** des résultats d'optimisation
- **La comparaison** de différentes approches
- **La présentation** des résultats
- **Le debugging** de l'algorithme
- **La compréhension** du processus d'optimisation
