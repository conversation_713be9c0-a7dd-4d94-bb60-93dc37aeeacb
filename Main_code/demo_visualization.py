# -*- coding: utf-8 -*-
"""
Script de démonstration des capacités de visualisation du parking
"""

import numpy as np
from constantes import *
from fonctions import *
from parking_visualizer import ParkingVisualizer, visualize_parking_optimization

def create_demo_parkings():
    """Crée des parkings de démonstration pour montrer les capacités de visualisation"""
    
    # Parking initial (mauvais) - très désordonné
    initial_parking = np.random.choice([NUM_MUR, NUM_ROUTE, NUM_PLACE], 
                                     size=(LARGEUR_PARKING, LONGUEUR_PARKING), 
                                     p=[0.4, 0.3, 0.3])
    
    # S'assurer que l'entrée et la sortie sont des routes
    for (i, j) in COORDS_ENTREES:
        initial_parking[i, j] = NUM_ROUTE
    for (i, j) in COORDS_SORTIES:
        initial_parking[i, j] = NUM_ROUTE
    
    # Parking optimisé (bon) - structure organisée
    optimized_parking = np.full((LARGEUR_PARKING, LONGUEUR_PARKING), NUM_MUR, dtype=int)
    
    # Créer une structure de parking organisée
    # Route principale verticale au centre
    center_col = LARGEUR_PARKING // 2
    optimized_parking[:, center_col] = NUM_ROUTE
    
    # Routes horizontales
    for row in [3, 7, 11]:
        optimized_parking[row, :] = NUM_ROUTE
    
    # Places de parking de chaque côté des routes
    for row in range(LARGEUR_PARKING):
        for col in range(LONGUEUR_PARKING):
            if optimized_parking[row, col] == NUM_MUR:
                # Ajouter des places près des routes
                if (row > 0 and optimized_parking[row-1, col] == NUM_ROUTE) or \
                   (row < LARGEUR_PARKING-1 and optimized_parking[row+1, col] == NUM_ROUTE) or \
                   (col > 0 and optimized_parking[row, col-1] == NUM_ROUTE) or \
                   (col < LONGUEUR_PARKING-1 and optimized_parking[row, col+1] == NUM_ROUTE):
                    if np.random.random() > 0.3:  # 70% de chance d'être une place
                        optimized_parking[row, col] = NUM_PLACE
    
    # S'assurer que l'entrée et la sortie sont des routes
    for (i, j) in COORDS_ENTREES:
        optimized_parking[i, j] = NUM_ROUTE
    for (i, j) in COORDS_SORTIES:
        optimized_parking[i, j] = NUM_ROUTE
    
    return initial_parking, optimized_parking

def create_evolution_demo():
    """Crée une évolution simulée pour la démonstration"""
    initial, final = create_demo_parkings()
    
    # Créer une évolution progressive de 10 étapes
    n_steps = 10
    evolution = []
    scores = []
    
    for i in range(n_steps):
        # Interpolation progressive entre initial et final
        alpha = i / (n_steps - 1)
        
        # Créer un parking intermédiaire
        current = initial.copy()
        
        # Progressivement remplacer des éléments par ceux du parking final
        mask = np.random.random(initial.shape) < alpha
        current[mask] = final[mask]
        
        evolution.append(current)
        
        # Score progressif (simulé)
        base_score = -10.0
        final_score = 95.0
        current_score = base_score + (final_score - base_score) * (alpha ** 0.5)
        scores.append(current_score)
    
    return evolution, scores

def main():
    print("=== DÉMONSTRATION DES CAPACITÉS DE VISUALISATION ===")
    
    # Créer des parkings de démonstration
    print("1. Création des parkings de démonstration...")
    initial_parking, optimized_parking = create_demo_parkings()
    
    # Calculer les scores (simulés pour la démo)
    initial_score = -8.5
    optimized_score = 94.2
    
    print(f"   Score initial: {initial_score}")
    print(f"   Score optimisé: {optimized_score}")
    print(f"   Amélioration: {optimized_score - initial_score:+.1f} points")
    
    # Créer l'évolution simulée
    print("2. Création de l'évolution simulée...")
    evolution_parkings, evolution_scores = create_evolution_demo()
    
    # Visualiser les résultats
    print("3. Génération des visualisations...")
    
    visualizer = ParkingVisualizer()
    
    # Comparaison avant/après
    print("   - Comparaison avant/après...")
    visualizer.plot_comparison(
        initial_parking, 
        optimized_parking, 
        initial_score, 
        optimized_score,
        save_path="demo_comparison.png",
        show_stats=True
    )
    
    # Évolution sur plusieurs étapes
    print("   - Évolution progressive...")
    visualizer.plot_evolution_grid(
        np.array(evolution_parkings), 
        evolution_scores,
        generations_to_show=[0, 2, 4, 6, 8, 9],
        save_path="demo_evolution.png"
    )
    
    # Parking individuel avec détails
    print("   - Parking optimisé détaillé...")
    import matplotlib.pyplot as plt
    fig, ax = plt.subplots(1, 1, figsize=(12, 12))
    visualizer.plot_single_parking(
        optimized_parking, 
        f"Parking Optimisé - Score: {optimized_score:.1f}",
        ax=ax,
        show_grid=True
    )
    
    # Ajouter des statistiques détaillées
    unique, counts = np.unique(optimized_parking, return_counts=True)
    stats_dict = dict(zip(unique, counts))
    total_cells = optimized_parking.size
    
    stats_text = f"""STATISTIQUES DU PARKING OPTIMISÉ:
• Places de parking: {stats_dict.get(NUM_PLACE, 0)} ({stats_dict.get(NUM_PLACE, 0)/total_cells*100:.1f}%)
• Routes: {stats_dict.get(NUM_ROUTE, 0)} ({stats_dict.get(NUM_ROUTE, 0)/total_cells*100:.1f}%)
• Murs/Obstacles: {stats_dict.get(NUM_MUR, 0)} ({stats_dict.get(NUM_MUR, 0)/total_cells*100:.1f}%)
• Score total: {optimized_score:.1f}/100"""
    
    fig.text(0.02, 0.98, stats_text, ha='left', va='top', fontsize=10, 
             bbox=dict(boxstyle="round,pad=0.5", facecolor="lightblue", alpha=0.8))
    
    plt.tight_layout()
    plt.savefig("demo_detailed.png", dpi=300, bbox_inches='tight')
    plt.close()
    
    print("   - Parking détaillé sauvegardé: demo_detailed.png")
    
    print("\n=== VISUALISATIONS CRÉÉES ===")
    print("✅ demo_comparison.png - Comparaison avant/après")
    print("✅ demo_evolution.png - Évolution progressive") 
    print("✅ demo_detailed.png - Parking optimisé détaillé")
    
    print(f"\n=== RÉSUMÉ ===")
    print(f"Amélioration totale: {optimized_score - initial_score:+.1f} points")
    print(f"Places de parking: {stats_dict.get(NUM_PLACE, 0)} places")
    print(f"Efficacité: {stats_dict.get(NUM_PLACE, 0)/total_cells*100:.1f}% de l'espace utilisé pour le parking")
    
    print("\n🎉 Démonstration terminée avec succès!")

if __name__ == "__main__":
    main()
