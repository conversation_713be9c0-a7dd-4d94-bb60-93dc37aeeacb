# -*- coding: utf-8 -*-
"""
Test case avec parking triangulaire connecté
Assure la connectivité entrée-sortie pour éviter les scores -inf
"""

import numpy as np
from constantes import *
from fonctions import *
from fonctions_algogen import *
from parking_visualizer import ParkingVisualizer, visualize_parking_optimization

def create_triangle_mask():
    """Crée un masque triangulaire"""
    mask = np.zeros((LARGEUR_PARKING, LONGUEUR_PARKING), dtype=bool)
    center_col = LONGUEUR_PARKING // 2
    
    for row in range(LARGEUR_PARKING):
        triangle_width = int((LARGEUR_PARKING - row) * 0.8)
        if triangle_width > 0:
            start_col = max(0, center_col - triangle_width // 2)
            end_col = min(LONGUEUR_PARKING, center_col + triangle_width // 2)
            mask[row, start_col:end_col] = True
    
    return mask

def create_connected_triangle_parking():
    """
    Crée un parking triangulaire avec connectivité garantie
    """
    parking = np.full((LARGEUR_PARKING, LONGUEUR_PARKING), NUM_MUR, dtype=int)
    triangle_mask = create_triangle_mask()
    center_col = LONGUEUR_PARKING // 2
    
    # 1. Créer une route principale verticale connectant entrée et sortie
    for row in range(LARGEUR_PARKING):
        if triangle_mask[row, center_col]:
            parking[row, center_col] = NUM_ROUTE
    
    # 2. Assurer l'entrée et la sortie
    for (i, j) in COORDS_ENTREES:
        if triangle_mask[i, j]:
            parking[i, j] = NUM_ROUTE
            # Connecter à la route principale
            if j != center_col:
                start_col = min(j, center_col)
                end_col = max(j, center_col)
                for c in range(start_col, end_col + 1):
                    if triangle_mask[i, c]:
                        parking[i, c] = NUM_ROUTE
    
    for (i, j) in COORDS_SORTIES:
        if triangle_mask[i, j]:
            parking[i, j] = NUM_ROUTE
            # Connecter à la route principale
            if j != center_col:
                start_col = min(j, center_col)
                end_col = max(j, center_col)
                for c in range(start_col, end_col + 1):
                    if triangle_mask[i, c]:
                        parking[i, c] = NUM_ROUTE
    
    # 3. Ajouter des routes secondaires horizontales
    for row in [LARGEUR_PARKING//4, LARGEUR_PARKING//2, 3*LARGEUR_PARKING//4]:
        for col in range(LONGUEUR_PARKING):
            if triangle_mask[row, col]:
                if np.random.random() < 0.6:  # 60% de chance d'être une route
                    parking[row, col] = NUM_ROUTE
    
    # 4. Remplir le reste avec des places de parking
    for row in range(LARGEUR_PARKING):
        for col in range(LONGUEUR_PARKING):
            if triangle_mask[row, col] and parking[row, col] == NUM_MUR:
                # Vérifier si adjacent à une route
                adjacent_to_road = False
                for dr, dc in [(-1,0), (1,0), (0,-1), (0,1)]:
                    nr, nc = row + dr, col + dc
                    if (0 <= nr < LARGEUR_PARKING and 0 <= nc < LONGUEUR_PARKING and 
                        parking[nr, nc] == NUM_ROUTE):
                        adjacent_to_road = True
                        break
                
                if adjacent_to_road:
                    parking[row, col] = NUM_PLACE if np.random.random() < 0.8 else NUM_MUR
                else:
                    parking[row, col] = NUM_PLACE if np.random.random() < 0.3 else NUM_MUR
    
    return parking

def create_optimized_triangle_parking():
    """
    Crée un parking triangulaire bien optimisé
    """
    parking = np.full((LARGEUR_PARKING, LONGUEUR_PARKING), NUM_MUR, dtype=int)
    triangle_mask = create_triangle_mask()
    center_col = LONGUEUR_PARKING // 2
    
    # Route principale verticale
    for row in range(LARGEUR_PARKING):
        if triangle_mask[row, center_col]:
            parking[row, center_col] = NUM_ROUTE
    
    # Routes horizontales régulières
    for row in range(2, LARGEUR_PARKING, 3):
        for col in range(LONGUEUR_PARKING):
            if triangle_mask[row, col]:
                parking[row, col] = NUM_ROUTE
    
    # Routes verticales secondaires
    for col in range(center_col - 4, center_col + 5, 4):
        if col != center_col and 0 <= col < LONGUEUR_PARKING:
            for row in range(LARGEUR_PARKING):
                if triangle_mask[row, col]:
                    parking[row, col] = NUM_ROUTE
    
    # Assurer entrée et sortie
    for (i, j) in COORDS_ENTREES:
        if triangle_mask[i, j]:
            parking[i, j] = NUM_ROUTE
    
    for (i, j) in COORDS_SORTIES:
        if triangle_mask[i, j]:
            parking[i, j] = NUM_ROUTE
    
    # Placer les places de parking
    for row in range(LARGEUR_PARKING):
        for col in range(LONGUEUR_PARKING):
            if triangle_mask[row, col] and parking[row, col] == NUM_MUR:
                # Vérifier si adjacent à une route
                adjacent_to_road = False
                for dr, dc in [(-1,0), (1,0), (0,-1), (0,1)]:
                    nr, nc = row + dr, col + dc
                    if (0 <= nr < LARGEUR_PARKING and 0 <= nc < LONGUEUR_PARKING and 
                        parking[nr, nc] == NUM_ROUTE):
                        adjacent_to_road = True
                        break
                
                if adjacent_to_road:
                    parking[row, col] = NUM_PLACE
    
    return parking

def test_triangle_connectivity():
    """
    Test la connectivité des parkings triangulaires
    """
    print("🔺 === TEST DE CONNECTIVITÉ TRIANGULAIRE ===")
    
    # Test 1: Parking connecté basique
    print("\n1. Test parking triangulaire connecté...")
    connected_parking = create_connected_triangle_parking()
    connected_score = score(connected_parking)
    print(f"   Score parking connecté: {connected_score:.2f}")
    
    # Test 2: Parking optimisé
    print("\n2. Test parking triangulaire optimisé...")
    optimized_parking = create_optimized_triangle_parking()
    optimized_score = score(optimized_parking)
    print(f"   Score parking optimisé: {optimized_score:.2f}")
    
    # Analyser les parkings
    def analyze_triangle_parking(parking, name):
        triangle_mask = create_triangle_mask()
        triangle_area = np.sum(triangle_mask)
        
        # Compter seulement dans la zone triangulaire
        triangle_content = parking[triangle_mask]
        unique, counts = np.unique(triangle_content, return_counts=True)
        stats = dict(zip(unique, counts))
        
        print(f"\n   {name}:")
        print(f"   - Zone triangulaire: {triangle_area} cellules")
        print(f"   - Places: {stats.get(NUM_PLACE, 0)} ({stats.get(NUM_PLACE, 0)/triangle_area*100:.1f}%)")
        print(f"   - Routes: {stats.get(NUM_ROUTE, 0)} ({stats.get(NUM_ROUTE, 0)/triangle_area*100:.1f}%)")
        print(f"   - Murs: {stats.get(NUM_MUR, 0)} ({stats.get(NUM_MUR, 0)/triangle_area*100:.1f}%)")
        
        return stats
    
    connected_stats = analyze_triangle_parking(connected_parking, "PARKING CONNECTÉ")
    optimized_stats = analyze_triangle_parking(optimized_parking, "PARKING OPTIMISÉ")
    
    # Visualiser les résultats
    print("\n3. Génération des visualisations...")
    
    visualizer = ParkingVisualizer()
    
    # Comparaison des parkings triangulaires
    visualizer.plot_comparison(
        connected_parking,
        optimized_parking,
        connected_score,
        optimized_score,
        save_path="triangle_connectivity_test.png",
        show_stats=True
    )
    print("   ✅ Test de connectivité sauvegardé: triangle_connectivity_test.png")
    
    # Vue détaillée du parking optimisé
    import matplotlib.pyplot as plt
    fig, ax = plt.subplots(1, 1, figsize=(12, 12))
    visualizer.plot_single_parking(
        optimized_parking,
        f"Parking Triangulaire Optimisé\nScore: {optimized_score:.1f}",
        ax=ax,
        show_grid=True
    )
    
    # Ajouter le masque triangulaire en overlay
    triangle_mask = create_triangle_mask()
    
    # Marquer les limites du triangle
    for row in range(LARGEUR_PARKING):
        for col in range(LONGUEUR_PARKING):
            if not triangle_mask[row, col]:
                # Marquer les zones hors triangle
                ax.add_patch(plt.Rectangle((col-0.5, row-0.5), 1, 1, 
                                         facecolor='red', alpha=0.2, edgecolor='red', linewidth=0.5))
    
    # Statistiques
    stats_text = f"""PARKING TRIANGULAIRE OPTIMISÉ:
🔺 Forme: Triangle (base={LARGEUR_PARKING-1}, hauteur={LARGEUR_PARKING})
📊 Score: {optimized_score:.1f} points
🅿️ Places: {optimized_stats.get(NUM_PLACE, 0)} ({optimized_stats.get(NUM_PLACE, 0)/np.sum(triangle_mask)*100:.1f}% du triangle)
🛣️ Routes: {optimized_stats.get(NUM_ROUTE, 0)} ({optimized_stats.get(NUM_ROUTE, 0)/np.sum(triangle_mask)*100:.1f}% du triangle)
✅ Connectivité: Entrée-Sortie garantie"""
    
    fig.text(0.02, 0.98, stats_text, ha='left', va='top', fontsize=10,
             bbox=dict(boxstyle="round,pad=0.5", facecolor="lightgreen", alpha=0.8))
    
    plt.tight_layout()
    plt.savefig("triangle_optimized_detailed.png", dpi=300, bbox_inches='tight')
    plt.close()
    print("   ✅ Vue détaillée sauvegardée: triangle_optimized_detailed.png")
    
    # Créer une évolution simulée réaliste
    print("\n4. Simulation d'évolution triangulaire...")
    evolution_parkings = []
    evolution_scores = []
    
    # Étapes d'évolution progressive
    steps = 6
    for i in range(steps):
        alpha = i / (steps - 1)
        
        # Interpolation entre connecté et optimisé
        current = connected_parking.copy()
        triangle_mask = create_triangle_mask()
        
        # Progressivement adopter la structure optimisée
        for row in range(LARGEUR_PARKING):
            for col in range(LONGUEUR_PARKING):
                if triangle_mask[row, col] and np.random.random() < alpha:
                    current[row, col] = optimized_parking[row, col]
        
        evolution_parkings.append(current)
        # Score interpolé
        evolution_scores.append(connected_score + (optimized_score - connected_score) * alpha)
    
    # Visualiser l'évolution
    visualizer.plot_evolution_grid(
        np.array(evolution_parkings),
        evolution_scores,
        save_path="triangle_evolution_realistic.png"
    )
    print("   ✅ Évolution réaliste sauvegardée: triangle_evolution_realistic.png")
    
    # Résumé
    print(f"\n🔺 === RÉSULTATS TEST TRIANGULAIRE ===")
    print(f"✅ Parking connecté: Score {connected_score:.2f}")
    print(f"✅ Parking optimisé: Score {optimized_score:.2f}")
    print(f"✅ Amélioration: {optimized_score - connected_score:+.2f} points")
    print(f"✅ Places optimisées: {connected_stats.get(NUM_PLACE, 0)} → {optimized_stats.get(NUM_PLACE, 0)}")
    
    print(f"\n📁 Fichiers générés:")
    print(f"   🔺 triangle_connectivity_test.png - Test de connectivité")
    print(f"   🔺 triangle_optimized_detailed.png - Vue détaillée optimisée")
    print(f"   🔺 triangle_evolution_realistic.png - Évolution réaliste")
    
    print(f"\n🎉 Test de connectivité triangulaire réussi!")
    
    return connected_parking, optimized_parking, connected_score, optimized_score

if __name__ == "__main__":
    test_triangle_connectivity()
