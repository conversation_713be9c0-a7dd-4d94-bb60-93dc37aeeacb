# -*- coding: utf-8 -*-
"""
Test case intégrant l'algorithme génétique avec un parking triangulaire
Démontre l'optimisation réelle d'une forme triangulaire
"""

import numpy as np
from constantes import *
from fonctions import *
from fonctions_algogen import *
from parking_visualizer import ParkingVisualizer, visualize_parking_optimization

def create_triangle_mask():
    """
    Crée un masque triangulaire pour contraindre la forme du parking
    """
    mask = np.zeros((LARGEUR_PARKING, LONGUEUR_PARKING), dtype=bool)
    center_col = LONGUEUR_PARKING // 2
    
    for row in range(LARGEUR_PARKING):
        # Triangle qui s'élargit vers le bas
        triangle_width = int((LARGEUR_PARKING - row) * 0.9)
        if triangle_width > 0:
            start_col = max(0, center_col - triangle_width // 2)
            end_col = min(LONGUEUR_PARKING, center_col + triangle_width // 2)
            mask[row, start_col:end_col] = True
    
    return mask

def create_triangle_parking_constrained():
    """
    Crée un parking aléatoire contraint par la forme triangulaire
    """
    parking = np.full((LARGEUR_PARKING, LONGUEUR_PARKING), NUM_MUR, dtype=int)
    triangle_mask = create_triangle_mask()
    
    # Remplir seulement l'intérieur du triangle
    for row in range(LARGEUR_PARKING):
        for col in range(LONGUEUR_PARKING):
            if triangle_mask[row, col]:
                parking[row, col] = np.random.choice([NUM_MUR, NUM_ROUTE, NUM_PLACE], 
                                                   p=[0.2, 0.4, 0.4])
    
    # Assurer l'entrée et la sortie
    for (i, j) in COORDS_ENTREES:
        if triangle_mask[i, j]:
            parking[i, j] = NUM_ROUTE
    
    for (i, j) in COORDS_SORTIES:
        if triangle_mask[i, j]:
            parking[i, j] = NUM_ROUTE
    
    return parking

def mutationTriangulaire(parking):
    """
    Mutation spécialisée pour les parkings triangulaires
    Respecte la contrainte de forme triangulaire
    """
    triangle_mask = create_triangle_mask()
    nouveau_parking = parking.copy()
    
    # Nombre de mutations (plus conservateur pour préserver la forme)
    nb_mutations = np.random.randint(1, 6)
    
    for _ in range(nb_mutations):
        # Choisir une position aléatoire dans le triangle
        valid_positions = np.where(triangle_mask)
        if len(valid_positions[0]) > 0:
            idx = np.random.randint(len(valid_positions[0]))
            i, j = valid_positions[0][idx], valid_positions[1][idx]
            
            # Ne pas muter l'entrée et la sortie
            if (i, j) in COORDS_ENTREES or (i, j) in COORDS_SORTIES:
                continue
            
            # Mutation intelligente basée sur le contexte
            current_value = nouveau_parking[i, j]
            
            # Compter les voisins de chaque type
            neighbors = []
            for di, dj in [(-1,0), (1,0), (0,-1), (0,1)]:
                ni, nj = i + di, j + dj
                if 0 <= ni < LARGEUR_PARKING and 0 <= nj < LONGUEUR_PARKING and triangle_mask[ni, nj]:
                    neighbors.append(nouveau_parking[ni, nj])
            
            route_neighbors = neighbors.count(NUM_ROUTE)
            place_neighbors = neighbors.count(NUM_PLACE)
            
            # Logique de mutation intelligente
            if current_value == NUM_MUR:
                if route_neighbors > 0:
                    nouveau_parking[i, j] = NUM_PLACE if np.random.random() < 0.7 else NUM_ROUTE
                else:
                    nouveau_parking[i, j] = NUM_ROUTE if np.random.random() < 0.5 else NUM_PLACE
            elif current_value == NUM_ROUTE:
                if route_neighbors < 2:  # Éviter de casser la connectivité
                    nouveau_parking[i, j] = NUM_PLACE if np.random.random() < 0.6 else NUM_MUR
            elif current_value == NUM_PLACE:
                if place_neighbors > 2:  # Éviter les gros blocs de places
                    nouveau_parking[i, j] = NUM_ROUTE if np.random.random() < 0.4 else NUM_MUR
    
    return nouveau_parking

def evolutionGenetiqueTriangulaire(generations=50):
    """
    Algorithme génétique spécialisé pour les parkings triangulaires
    """
    print(f"Démarrage de l'évolution génétique triangulaire ({generations} générations)...")
    
    # Population initiale de parkings triangulaires
    population_size = 20  # Plus petite population pour les tests
    population = []
    
    for _ in range(population_size):
        parking = create_triangle_parking_constrained()
        score_parking = score(parking)
        population.append((parking, score_parking))
    
    # Trier par score
    population.sort(key=lambda x: x[1], reverse=True)
    
    evolution_parkings = []
    evolution_scores = []
    
    for generation in range(generations):
        # Sauvegarder le meilleur de cette génération
        best_parking, best_score = population[0]
        evolution_parkings.append(best_parking.copy())
        evolution_scores.append(best_score)
        
        print(f"Génération {generation}: Meilleur score = {best_score:.2f}")
        
        # Sélection des parents (top 50%)
        parents = population[:population_size//2]
        
        # Créer la nouvelle génération
        nouvelle_population = []
        
        # Garder les meilleurs (élitisme)
        nouvelle_population.extend(parents[:population_size//4])
        
        # Générer des descendants
        while len(nouvelle_population) < population_size:
            # Sélection des parents
            parent1 = parents[np.random.randint(len(parents))][0]
            parent2 = parents[np.random.randint(len(parents))][0]
            
            # Croisement simple (mélange aléatoire)
            enfant = parent1.copy()
            triangle_mask = create_triangle_mask()
            
            for i in range(LARGEUR_PARKING):
                for j in range(LONGUEUR_PARKING):
                    if triangle_mask[i, j] and np.random.random() < 0.5:
                        enfant[i, j] = parent2[i, j]
            
            # Mutation
            if np.random.random() < 0.8:  # 80% de chance de mutation
                enfant = mutationTriangulaire(enfant)
            
            # Évaluer l'enfant
            score_enfant = score(enfant)
            nouvelle_population.append((enfant, score_enfant))
        
        # Trier la nouvelle population
        nouvelle_population.sort(key=lambda x: x[1], reverse=True)
        population = nouvelle_population[:population_size]
    
    # Sauvegarder la dernière génération
    best_parking, best_score = population[0]
    evolution_parkings.append(best_parking.copy())
    evolution_scores.append(best_score)
    
    print(f"Évolution terminée. Score final: {best_score:.2f}")
    
    return evolution_parkings, evolution_scores, population

def main():
    print("🔺 === TEST GÉNÉTIQUE TRIANGULAIRE ===")
    print("Optimisation réelle d'un parking triangulaire avec l'algorithme génétique")
    
    # Créer un parking initial triangulaire
    print("\n1. Création du parking triangulaire initial...")
    initial_parking = create_triangle_parking_constrained()
    initial_score = score(initial_parking)
    print(f"   Score initial: {initial_score:.2f}")
    
    # Lancer l'évolution génétique triangulaire
    print("\n2. Lancement de l'optimisation génétique triangulaire...")
    evolution_parkings, evolution_scores, final_population = evolutionGenetiqueTriangulaire(generations=30)
    
    # Récupérer le meilleur résultat
    best_parking, best_score = final_population[0]
    
    print(f"\n3. Résultats de l'optimisation:")
    print(f"   Score initial: {initial_score:.2f}")
    print(f"   Score final: {best_score:.2f}")
    print(f"   Amélioration: {best_score - initial_score:+.2f} points")
    
    # Analyser les résultats
    def analyze_triangle(parking, name):
        triangle_mask = create_triangle_mask()
        triangle_area = np.sum(triangle_mask)
        
        unique, counts = np.unique(parking[triangle_mask], return_counts=True)
        stats = dict(zip(unique, counts))
        
        print(f"\n   {name}:")
        print(f"   - Surface triangulaire: {triangle_area} cellules")
        print(f"   - Places: {stats.get(NUM_PLACE, 0)} ({stats.get(NUM_PLACE, 0)/triangle_area*100:.1f}%)")
        print(f"   - Routes: {stats.get(NUM_ROUTE, 0)} ({stats.get(NUM_ROUTE, 0)/triangle_area*100:.1f}%)")
        print(f"   - Murs: {stats.get(NUM_MUR, 0)} ({stats.get(NUM_MUR, 0)/triangle_area*100:.1f}%)")
        
        return stats
    
    initial_stats = analyze_triangle(initial_parking, "PARKING INITIAL")
    final_stats = analyze_triangle(best_parking, "PARKING OPTIMISÉ")
    
    # Visualiser les résultats
    print("\n4. Génération des visualisations...")
    
    visualizer = ParkingVisualizer()
    
    # Comparaison génétique triangulaire
    visualizer.plot_comparison(
        initial_parking,
        best_parking,
        initial_score,
        best_score,
        save_path="triangle_genetic_comparison.png",
        show_stats=True
    )
    print("   ✅ Comparaison sauvegardée: triangle_genetic_comparison.png")
    
    # Évolution génétique réelle
    generations_to_show = [0, 5, 10, 15, 20, 25, 29]
    if len(evolution_parkings) >= max(generations_to_show) + 1:
        visualizer.plot_evolution_grid(
            np.array(evolution_parkings),
            evolution_scores,
            generations_to_show=generations_to_show,
            save_path="triangle_genetic_evolution.png"
        )
        print("   ✅ Évolution sauvegardée: triangle_genetic_evolution.png")
    
    # Graphique des scores
    import matplotlib.pyplot as plt
    plt.figure(figsize=(12, 6))
    plt.plot(evolution_scores, 'b-', linewidth=2, marker='o', markersize=4)
    plt.title('Évolution du Score - Algorithme Génétique Triangulaire', fontsize=14, fontweight='bold')
    plt.xlabel('Génération')
    plt.ylabel('Score')
    plt.grid(True, alpha=0.3)
    plt.axhline(y=0, color='r', linestyle='--', alpha=0.5, label='Score neutre')
    
    # Annotations
    if len(evolution_scores) > 0:
        plt.annotate(f'Score initial: {evolution_scores[0]:.1f}', 
                    xy=(0, evolution_scores[0]), xytext=(5, evolution_scores[0] + 5),
                    arrowprops=dict(arrowstyle='->', color='red'))
        plt.annotate(f'Score final: {evolution_scores[-1]:.1f}', 
                    xy=(len(evolution_scores)-1, evolution_scores[-1]), 
                    xytext=(len(evolution_scores)-6, evolution_scores[-1] + 5),
                    arrowprops=dict(arrowstyle='->', color='green'))
    
    plt.legend()
    plt.tight_layout()
    plt.savefig('triangle_genetic_scores.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("   ✅ Graphique des scores sauvegardé: triangle_genetic_scores.png")
    
    # Résumé final
    print(f"\n🔺 === RÉSULTATS FINAUX ===")
    print(f"✅ Optimisation génétique triangulaire réussie!")
    print(f"✅ Amélioration: {initial_score:.2f} → {best_score:.2f} ({best_score - initial_score:+.2f} points)")
    print(f"✅ Places optimisées: {initial_stats.get(NUM_PLACE, 0)} → {final_stats.get(NUM_PLACE, 0)}")
    print(f"✅ Connectivité améliorée: {initial_stats.get(NUM_ROUTE, 0)} → {final_stats.get(NUM_ROUTE, 0)} routes")
    
    print(f"\n📁 Fichiers générés:")
    print(f"   🔺 triangle_genetic_comparison.png")
    print(f"   🔺 triangle_genetic_evolution.png") 
    print(f"   🔺 triangle_genetic_scores.png")
    
    print(f"\n🎉 Test génétique triangulaire terminé avec succès!")

if __name__ == "__main__":
    main()
