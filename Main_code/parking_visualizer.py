# -*- coding: utf-8 -*-
"""
Module de visualisation des parkings - affichage des layouts avant et après optimisation
"""

import numpy as np
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.colors import ListedColormap
from constantes import *

class ParkingVisualizer:
    """Classe pour visualiser les parkings avec des couleurs et légendes claires"""
    
    def __init__(self):
        # Définir les couleurs pour chaque type de cellule
        self.colors = {
            NUM_MUR: '#2C3E50',      # Bleu foncé pour les murs
            NUM_ROUTE: '#ECF0F1',    # Gris clair pour les routes
            NUM_PLACE: '#27AE60',    # Vert pour les places de parking
            NUM_VOITURE: '#E74C3C'   # Rouge pour les voitures
        }
        
        # Créer une colormap personnalisée
        color_list = [self.colors[NUM_MUR], self.colors[NUM_ROUTE], 
                     self.colors[NUM_PLACE], self.colors[NUM_VOITURE]]
        self.cmap = ListedColormap(color_list)
        
        # Labels pour la légende
        self.labels = {
            NUM_MUR: 'Murs/Obstacles',
            NUM_ROUTE: 'Routes',
            NUM_PLACE: 'Places de parking',
            NUM_VOITURE: 'Voitures'
        }

    def plot_single_parking(self, parking, title="Parking Layout", ax=None, show_grid=True):
        """
        Affiche un seul parking avec une légende
        
        Args:
            parking: numpy array représentant le parking
            title: titre du graphique
            ax: axes matplotlib (optionnel)
            show_grid: afficher la grille
        """
        if ax is None:
            fig, ax = plt.subplots(1, 1, figsize=(10, 10))
        
        # Afficher le parking
        im = ax.imshow(parking, cmap=self.cmap, vmin=-1, vmax=2)
        
        # Ajouter la grille
        if show_grid:
            ax.set_xticks(np.arange(-0.5, parking.shape[1], 1), minor=True)
            ax.set_yticks(np.arange(-0.5, parking.shape[0], 1), minor=True)
            ax.grid(which="minor", color="black", linestyle='-', linewidth=0.5, alpha=0.3)
        
        # Marquer l'entrée et la sortie
        for (i, j) in COORDS_ENTREES:
            ax.add_patch(patches.Rectangle((j-0.4, i-0.4), 0.8, 0.8, 
                                         linewidth=3, edgecolor='blue', facecolor='none'))
            ax.text(j, i-0.6, 'ENTRÉE', ha='center', va='center', 
                   fontsize=8, fontweight='bold', color='blue')
        
        for (i, j) in COORDS_SORTIES:
            ax.add_patch(patches.Rectangle((j-0.4, i-0.4), 0.8, 0.8, 
                                         linewidth=3, edgecolor='orange', facecolor='none'))
            ax.text(j, i+0.6, 'SORTIE', ha='center', va='center', 
                   fontsize=8, fontweight='bold', color='orange')
        
        ax.set_title(title, fontsize=14, fontweight='bold')
        ax.set_xlabel('Colonnes')
        ax.set_ylabel('Lignes')
        
        return ax

    def plot_comparison(self, initial_parking, final_parking, initial_score, final_score, 
                       save_path=None, show_stats=True):
        """
        Compare deux parkings côte à côte (avant/après optimisation)
        
        Args:
            initial_parking: parking initial
            final_parking: parking optimisé
            initial_score: score du parking initial
            final_score: score du parking final
            save_path: chemin pour sauvegarder l'image
            show_stats: afficher les statistiques
        """
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 10))
        
        # Parking initial
        self.plot_single_parking(initial_parking, 
                                f"Parking Initial\nScore: {initial_score:.2f}", 
                                ax=ax1)
        
        # Parking optimisé
        self.plot_single_parking(final_parking, 
                                f"Parking Optimisé\nScore: {final_score:.2f}", 
                                ax=ax2)
        
        # Ajouter une légende commune
        legend_elements = []
        for value, label in self.labels.items():
            legend_elements.append(patches.Patch(color=self.colors[value], label=label))
        
        fig.legend(handles=legend_elements, loc='upper center', bbox_to_anchor=(0.5, 0.02), 
                  ncol=4, fontsize=12)
        
        # Afficher les statistiques si demandé
        if show_stats:
            stats_text = self._get_parking_stats(initial_parking, final_parking, 
                                                initial_score, final_score)
            fig.text(0.5, 0.95, stats_text, ha='center', va='top', fontsize=12, 
                    bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.8))
        
        plt.tight_layout()
        plt.subplots_adjust(bottom=0.1, top=0.85)
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Visualisation sauvegardée: {save_path}")

        plt.close()  # Close instead of show for non-interactive mode

    def plot_evolution_grid(self, parking_evolution, scores, generations_to_show=None, 
                           save_path=None):
        """
        Affiche l'évolution du parking sur plusieurs générations dans une grille
        
        Args:
            parking_evolution: array 3D (generations, height, width)
            scores: scores correspondants
            generations_to_show: liste des générations à afficher (par défaut: début, milieu, fin)
            save_path: chemin pour sauvegarder
        """
        if generations_to_show is None:
            n_gen = len(parking_evolution)
            generations_to_show = [0, n_gen//4, n_gen//2, 3*n_gen//4, n_gen-1]
        
        n_plots = len(generations_to_show)
        cols = min(3, n_plots)
        rows = (n_plots + cols - 1) // cols
        
        fig, axes = plt.subplots(rows, cols, figsize=(6*cols, 6*rows))
        if n_plots == 1:
            axes = [axes]
        elif rows == 1:
            axes = axes.reshape(1, -1)
        
        for idx, gen in enumerate(generations_to_show):
            row, col = idx // cols, idx % cols
            ax = axes[row, col] if rows > 1 else axes[col]
            
            self.plot_single_parking(parking_evolution[gen], 
                                   f"Génération {gen}\nScore: {scores[gen]:.2f}", 
                                   ax=ax, show_grid=False)
        
        # Masquer les axes inutilisés
        for idx in range(n_plots, rows * cols):
            row, col = idx // cols, idx % cols
            ax = axes[row, col] if rows > 1 else axes[col]
            ax.set_visible(False)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Évolution sauvegardée: {save_path}")

        plt.close()  # Close instead of show for non-interactive mode

    def _get_parking_stats(self, initial_parking, final_parking, initial_score, final_score):
        """Calcule et formate les statistiques des parkings"""
        
        def count_elements(parking):
            unique, counts = np.unique(parking, return_counts=True)
            return dict(zip(unique, counts))
        
        initial_counts = count_elements(initial_parking)
        final_counts = count_elements(final_parking)
        
        total_cells = initial_parking.size
        
        stats = f"AMÉLIORATION: {final_score - initial_score:+.2f} points | "
        stats += f"Places initiales: {initial_counts.get(NUM_PLACE, 0)} → "
        stats += f"Places finales: {final_counts.get(NUM_PLACE, 0)} | "
        stats += f"Routes initiales: {initial_counts.get(NUM_ROUTE, 0)} → "
        stats += f"Routes finales: {final_counts.get(NUM_ROUTE, 0)}"
        
        return stats

def visualize_parking_optimization(initial_parking, final_parking, initial_score, 
                                 final_score, evolution_data=None):
    """
    Fonction principale pour visualiser l'optimisation d'un parking
    
    Args:
        initial_parking: parking de départ
        final_parking: parking optimisé
        initial_score: score initial
        final_score: score final
        evolution_data: tuple (parkings_evolution, scores_evolution) optionnel
    """
    visualizer = ParkingVisualizer()
    
    # Comparaison avant/après
    visualizer.plot_comparison(initial_parking, final_parking, 
                              initial_score, final_score,
                              save_path="parking_comparison.png")
    
    # Évolution si les données sont disponibles
    if evolution_data is not None:
        parkings_evolution, scores_evolution = evolution_data
        visualizer.plot_evolution_grid(parkings_evolution, scores_evolution,
                                     save_path="parking_evolution.png")

# Fonction utilitaire pour créer un parking de test
def create_test_parking():
    """Crée un parking de test pour démonstration"""
    parking = np.random.choice([NUM_MUR, NUM_ROUTE, NUM_PLACE], 
                              size=(LARGEUR_PARKING, LONGUEUR_PARKING), 
                              p=[0.3, 0.3, 0.4])
    
    # S'assurer que l'entrée et la sortie sont des routes
    for (i, j) in COORDS_ENTREES:
        parking[i, j] = NUM_ROUTE
    for (i, j) in COORDS_SORTIES:
        parking[i, j] = NUM_ROUTE
    
    return parking

if __name__ == "__main__":
    # Test du module
    print("Test du module de visualisation...")
    
    # Créer des parkings de test
    initial = create_test_parking()
    final = create_test_parking()
    
    # Simuler des scores
    initial_score = -5.2
    final_score = 85.7
    
    # Visualiser
    visualize_parking_optimization(initial, final, initial_score, final_score)
