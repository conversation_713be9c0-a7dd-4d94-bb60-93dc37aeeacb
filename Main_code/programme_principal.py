# -*- coding: utf-8 -*-
from constantes import *
from fonctions import *
from fonctions_affichage import *
from fonctions_techiques import *
from parking_visualizer import ParkingVisualizer, visualize_parking_optimization
from quantum_parking_optimizer import optimize_parking_quantum, QuantumParkingOptimizer

## Début du programme

def main():
    print("🚀 === PARKING LOT OPTIMIZATION SYSTEM ===")
    print("Choose optimization method:")
    print("1. Genetic Algorithm (Traditional)")
    print("2. Quantum Annealing (QUBO)")
    print("3. Hybrid Approach (Both)")

    choice = input("Enter choice (1/2/3): ").strip()

    # Créer un parking initial pour comparaison
    print("\n📋 Création du parking initial...")
    initial_parking = creationRandomParking(LONGUEUR_PARKING, LARGEUR_PARKING)
    initial_score = score(initial_parking)
    print(f"Score du parking initial: {initial_score:.2f}")

    if choice == "2":
        # Quantum-only approach
        print("\n⚛️ Lancement de l'optimisation quantique...")
        quantum_parking, quantum_energy, optimizer = optimize_parking_quantum(use_quantum=False)
        quantum_score = score(quantum_parking)

        print(f"Score du parking quantique: {quantum_score:.2f}")
        print(f"Énergie QUBO: {quantum_energy:.2f}")
        print(f"Amélioration: {quantum_score - initial_score:+.2f} points")

        # Visualiser les résultats quantiques
        print("\n🎨 Génération des visualisations quantiques...")
        visualizer = ParkingVisualizer()
        visualizer.plot_comparison(
            initial_parking,
            quantum_parking,
            initial_score,
            quantum_score,
            save_path="quantum_parking_comparison.png",
            show_stats=True
        )
        print("✅ Visualisation quantique sauvegardée: quantum_parking_comparison.png")

    elif choice == "3":
        # Hybrid approach
        print("\n🔬 Lancement de l'approche hybride...")

        # 1. Quantum initialization
        print("   Phase 1: Optimisation quantique initiale...")
        quantum_parking, quantum_energy, optimizer = optimize_parking_quantum(use_quantum=False)
        quantum_score = score(quantum_parking)
        print(f"   Score quantique initial: {quantum_score:.2f}")

        # 2. Genetic refinement (shortened)
        print("   Phase 2: Raffinement génétique...")
        # Use quantum result as starting point for genetic algorithm
        # (This would require modifying the genetic algorithm to accept initial population)
        evolParkings, derniereGenxScores, evolScores = evolutionGenetique()

        best_parking = derniereGenxScores[0][0]
        best_score = derniereGenxScores[0][1]

        print(f"   Score final hybride: {best_score:.2f}")
        print(f"   Amélioration totale: {best_score - initial_score:+.2f} points")

        # Visualisation hybride
        print("\n🎨 Génération des visualisations hybrides...")

        # Three-way comparison
        import matplotlib.pyplot as plt
        visualizer = ParkingVisualizer()

        fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(24, 8))

        visualizer.plot_single_parking(initial_parking, f"Initial\nScore: {initial_score:.1f}", ax=ax1)
        visualizer.plot_single_parking(quantum_parking, f"Quantum\nScore: {quantum_score:.1f}", ax=ax2)
        visualizer.plot_single_parking(best_parking, f"Hybrid\nScore: {best_score:.1f}", ax=ax3)

        plt.tight_layout()
        plt.savefig("hybrid_parking_comparison.png", dpi=300, bbox_inches='tight')
        plt.close()
        print("✅ Comparaison hybride sauvegardée: hybrid_parking_comparison.png")

    else:
        # Traditional genetic algorithm
        print("\n🧬 Lancement de l'optimisation génétique...")
        evolParkings, derniereGenxScores, evolScores = evolutionGenetique()

        best_parking = derniereGenxScores[0][0]
        best_score = derniereGenxScores[0][1]

        print(f"Score du parking optimisé: {best_score:.2f}")
        print(f"Amélioration: {best_score - initial_score:+.2f} points")

        # Sauvegarder les données
        saveData(evolParkings[0], "parkingsDerniereGenScore_{score}.csv".format(score=best_score))

        # Visualiser les résultats
        print("\n🎨 Génération des visualisations...")
        visualize_parking_optimization(
            initial_parking,
            best_parking,
            initial_score,
            best_score,
            evolution_data=(evolParkings, evolScores)
        )
    print(f"\n🎉 Optimization complete!")


if __name__ == '__main__':
    main()

'''

>> Conseil: dès que stagne, remplacer 3/4 des parkings de façon random && garder minimum local (maybe niquel)
>> Bizarre: vérif qd nuree_moy_garage != n_iteration

Idées à tester:
>> algo génétique progressif ?
>> Jviens de mettre une mutation random dès que stagne mais pb: modifie trop parkings et pabo score: dispersion de fou au nv scores
    -> Maybe faire plutôt avec fct ajoutBoutRoute ?
    -> Ajouter chance mutation aléa pour chacun des enfants >> diversité génétique ?


Problèmes (on ignore sagement):
- Les parkings se ressemblent tous: limite évolution
- Croisement inutile ?

A faire:
- Ajout autres croisements: 1 case/2, en 1 pt
- Ajout autres mutations: mutation avec plusieurs mutations de bloc de route

Idées (je le ferai jamais):
- Si stagnation, lancer plusieurs algo génétiques en même temps >> intervertir parkings entre eux

But actuel:
- Tester algo génétique avec différents scores >> différents parkings puis comparer
parkings avec score A* long >> voir meilleure méthode
- Faire varier tous les paramètres à la fin >> voir affectation

'''
