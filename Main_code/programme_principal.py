# -*- coding: utf-8 -*-
from constantes import *
from fonctions import *
from fonctions_affichage import *
from fonctions_techiques import *
from parking_visualizer import ParkingVisualizer, visualize_parking_optimization

## Début du programme

def main():
    # Créer un parking initial pour comparaison
    print("Création du parking initial...")
    initial_parking = creationRandomParking(LONGUEUR_PARKING, LARGEUR_PARKING)
    initial_score = score(initial_parking)
    print(f"Score du parking initial: {initial_score:.2f}")

    # Lancer l'optimisation génétique
    print("Lancement de l'optimisation génétique...")
    evolParkings, derniereGenxScores, evolScores = evolutionGenetique()

    # Récupérer le meilleur parking et son score
    best_parking = derniereGenxScores[0][0]  # Meilleur parking de la dernière génération
    best_score = derniereGenxScores[0][1]    # Son score

    print(f"Score du parking optimisé: {best_score:.2f}")
    print(f"Amélioration: {best_score - initial_score:+.2f} points")

    # Sauvegarder les données
    saveData(evolParkings[0], "parkingsDerniereGenScore_{score}.csv".format(score=best_score))

    # Visualiser les résultats
    print("Génération des visualisations...")

    # Visualisation comparative avec les vraies données d'évolution
    visualize_parking_optimization(
        initial_parking,
        best_parking,
        initial_score,
        best_score,
        evolution_data=(evolParkings, evolScores)
    )
    
    
    evolDerniereGen = np.zeros((N_PARKINGS,LARGEUR_PARKING,LONGUEUR_PARKING))
    for i in range(N_PARKINGS):
        evolDerniereGen[i] = derniereGenxScores[i][0]

    #affichageLoop(evolDerniereGen)
    #affichageLoop(evolParkings)

    # stop programme dès que parkings liés pour mesurer nb itérations >>  10 000 >> moyenne + écart-type + ??


if __name__ == '__main__':
    main()

'''

>> Conseil: dès que stagne, remplacer 3/4 des parkings de façon random && garder minimum local (maybe niquel)
>> Bizarre: vérif qd nuree_moy_garage != n_iteration

Idées à tester:
>> algo génétique progressif ?
>> Jviens de mettre une mutation random dès que stagne mais pb: modifie trop parkings et pabo score: dispersion de fou au nv scores
    -> Maybe faire plutôt avec fct ajoutBoutRoute ?
    -> Ajouter chance mutation aléa pour chacun des enfants >> diversité génétique ?


Problèmes (on ignore sagement):
- Les parkings se ressemblent tous: limite évolution
- Croisement inutile ?

A faire:
- Ajout autres croisements: 1 case/2, en 1 pt
- Ajout autres mutations: mutation avec plusieurs mutations de bloc de route

Idées (je le ferai jamais):
- Si stagnation, lancer plusieurs algo génétiques en même temps >> intervertir parkings entre eux

But actuel:
- Tester algo génétique avec différents scores >> différents parkings puis comparer
parkings avec score A* long >> voir meilleure méthode
- Faire varier tous les paramètres à la fin >> voir affectation

'''
