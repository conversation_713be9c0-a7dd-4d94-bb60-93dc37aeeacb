# -*- coding: utf-8 -*-
"""
Quantum Annealing QUBO Optimizer for Parking Lot Layout
Based on research insights from PCB routing optimization
"""

import numpy as np
from constantes import *
from fonctions_techiques import *
from itertools import product
import random

try:
    from dimod import BinaryQuadraticModel, ExactSolver
    from dwave.system import D<PERSON>aveSampler, EmbeddingComposite
    QUANTUM_AVAILABLE = True
except ImportError:
    QUANTUM_AVAILABLE = False
    print("Warning: D-Wave Ocean SDK not available. Using classical simulation.")

class QuantumParkingOptimizer:
    """
    QUBO-based parking lot optimizer using quantum annealing principles
    """
    
    def __init__(self, width=15, height=15):
        self.width = width
        self.height = height
        self.num_types = 3  # road, parking, wall
        self.num_vars = width * height * self.num_types
        
        # QUBO weights (based on research insights)
        self.alpha = 10000  # Connectivity penalty
        self.beta = 100     # Efficiency reward
        self.gamma = 50     # Traffic flow penalty
        self.delta = 50000  # Constraint penalty
        
        # Pre-compute candidate paths (inspired by PCB routing research)
        self.candidate_paths = self._generate_candidate_paths()
        
    def _var_index(self, i, j, k):
        """Convert (row, col, type) to linear variable index"""
        return i * self.width * self.num_types + j * self.num_types + k
    
    def _index_to_var(self, idx):
        """Convert linear index back to (row, col, type)"""
        i = idx // (self.width * self.num_types)
        remainder = idx % (self.width * self.num_types)
        j = remainder // self.num_types
        k = remainder % self.num_types
        return i, j, k
    
    def _generate_candidate_paths(self):
        """
        Generate candidate paths from entrance to exit
        Inspired by PCB routing research approach
        """
        paths = []
        
        # Get entrance and exit coordinates
        entrance = list(COORDS_ENTREES)[0] if COORDS_ENTREES else (self.height//2, 0)
        exit_coord = list(COORDS_SORTIES)[0] if COORDS_SORTIES else (self.height//2, self.width-1)
        
        # Generate multiple candidate paths using different strategies
        strategies = [
            'direct',      # Direct path
            'detour_up',   # Detour through upper area
            'detour_down', # Detour through lower area
            'zigzag',      # Zigzag pattern
            'perimeter'    # Along perimeter
        ]
        
        for strategy in strategies:
            path = self._generate_path_by_strategy(entrance, exit_coord, strategy)
            if path:
                paths.append(path)
        
        return paths
    
    def _generate_path_by_strategy(self, start, end, strategy):
        """Generate a path using specific strategy"""
        if strategy == 'direct':
            return self._direct_path(start, end)
        elif strategy == 'detour_up':
            return self._detour_path(start, end, 'up')
        elif strategy == 'detour_down':
            return self._detour_path(start, end, 'down')
        elif strategy == 'zigzag':
            return self._zigzag_path(start, end)
        elif strategy == 'perimeter':
            return self._perimeter_path(start, end)
        return []
    
    def _direct_path(self, start, end):
        """Generate direct path between start and end"""
        path = []
        i1, j1 = start
        i2, j2 = end
        
        # Horizontal movement
        for j in range(min(j1, j2), max(j1, j2) + 1):
            path.append((i1, j))
        
        # Vertical movement
        for i in range(min(i1, i2), max(i1, i2) + 1):
            if (i, j2) not in path:
                path.append((i, j2))
        
        return path
    
    def _detour_path(self, start, end, direction):
        """Generate path with detour in specified direction"""
        path = []
        i1, j1 = start
        i2, j2 = end
        
        # Detour point
        detour_i = 2 if direction == 'up' else self.height - 3
        
        # Path to detour point
        for j in range(j1, self.width//2 + 1):
            path.append((i1, j))
        for i in range(min(i1, detour_i), max(i1, detour_i) + 1):
            path.append((i, self.width//2))
        
        # Path from detour to end
        for j in range(self.width//2, j2 + 1):
            path.append((detour_i, j))
        for i in range(min(detour_i, i2), max(detour_i, i2) + 1):
            path.append((i, j2))
        
        return list(set(path))  # Remove duplicates
    
    def _zigzag_path(self, start, end):
        """Generate zigzag path"""
        path = []
        i1, j1 = start
        i2, j2 = end
        
        current_i, current_j = i1, j1
        
        while current_j < j2:
            # Move right
            path.append((current_i, current_j))
            current_j += 1
            
            # Zigzag vertically
            if current_j % 3 == 0:
                zigzag_i = current_i + 2 if current_i < self.height//2 else current_i - 2
                zigzag_i = max(0, min(self.height-1, zigzag_i))
                path.append((zigzag_i, current_j))
                current_i = zigzag_i
        
        # Final connection to end
        for i in range(min(current_i, i2), max(current_i, i2) + 1):
            path.append((i, j2))
        
        return path
    
    def _perimeter_path(self, start, end):
        """Generate path along perimeter"""
        path = []
        i1, j1 = start
        i2, j2 = end
        
        # Go to top edge
        for i in range(i1, 0, -1):
            path.append((i, j1))
        
        # Along top edge
        for j in range(j1, j2 + 1):
            path.append((0, j))
        
        # Down to end
        for i in range(0, i2 + 1):
            path.append((i, j2))
        
        return path
    
    def construct_qubo_matrix(self):
        """
        Construct QUBO matrix based on research methodology
        """
        Q = np.zeros((self.num_vars, self.num_vars))
        
        # 1. One-hot constraints (exactly one type per cell)
        self._add_onehot_constraints(Q)
        
        # 2. Connectivity constraints (path-based approach from research)
        self._add_connectivity_constraints(Q)
        
        # 3. Efficiency rewards (parking adjacent to roads)
        self._add_efficiency_rewards(Q)
        
        # 4. Traffic flow optimization
        self._add_traffic_flow_terms(Q)
        
        return Q
    
    def _add_onehot_constraints(self, Q):
        """Add one-hot constraints: exactly one type per cell"""
        for i in range(self.height):
            for j in range(self.width):
                # Get variable indices for this cell
                indices = [self._var_index(i, j, k) for k in range(self.num_types)]
                
                # Diagonal terms: -2δ for each variable
                for idx in indices:
                    Q[idx, idx] += self.delta * (-2)
                
                # Cross terms: +2δ for each pair
                for idx1 in range(len(indices)):
                    for idx2 in range(idx1 + 1, len(indices)):
                        Q[indices[idx1], indices[idx2]] += self.delta * 2
    
    def _add_connectivity_constraints(self, Q):
        """Add connectivity constraints using candidate paths"""
        for path in self.candidate_paths:
            path_weight = len(path)  # Longer paths have higher penalty
            
            # For each path, add penalty if not all cells are roads
            for i, (row, col) in enumerate(path):
                if 0 <= row < self.height and 0 <= col < self.width:
                    road_var = self._var_index(row, col, 0)  # Road type = 0
                    
                    # Reward for selecting road on path
                    Q[road_var, road_var] += -self.alpha / path_weight
                    
                    # Penalty for not selecting road on critical path segments
                    if i < len(path) // 3 or i > 2 * len(path) // 3:  # Critical segments
                        non_road_vars = [self._var_index(row, col, 1), self._var_index(row, col, 2)]
                        for var in non_road_vars:
                            Q[var, var] += self.alpha / path_weight
    
    def _add_efficiency_rewards(self, Q):
        """Add efficiency rewards for parking spaces adjacent to roads"""
        for i in range(self.height):
            for j in range(self.width):
                parking_var = self._var_index(i, j, 1)  # Parking type = 1
                
                # Check adjacent cells
                for di, dj in [(-1,0), (1,0), (0,-1), (0,1)]:
                    ni, nj = i + di, j + dj
                    if 0 <= ni < self.height and 0 <= nj < self.width:
                        road_var = self._var_index(ni, nj, 0)  # Adjacent road
                        
                        # Reward for parking adjacent to road
                        Q[parking_var, road_var] += -self.beta
    
    def _add_traffic_flow_terms(self, Q):
        """Add traffic flow optimization terms"""
        entrance = list(COORDS_ENTREES)[0] if COORDS_ENTREES else (self.height//2, 0)
        exit_coord = list(COORDS_SORTIES)[0] if COORDS_SORTIES else (self.height//2, self.width-1)
        
        for i in range(self.height):
            for j in range(self.width):
                # Distance penalty from entrance and exit
                dist_entrance = abs(i - entrance[0]) + abs(j - entrance[1])
                dist_exit = abs(i - exit_coord[0]) + abs(j - exit_coord[1])
                
                # Average distance penalty
                avg_distance = (dist_entrance + dist_exit) / 2
                distance_penalty = self.gamma * avg_distance / (self.height + self.width)
                
                # Apply penalty to road variables (prefer roads closer to entrance/exit)
                road_var = self._var_index(i, j, 0)
                Q[road_var, road_var] += distance_penalty
    
    def solve_qubo(self, use_quantum=True, num_reads=1000):
        """
        Solve QUBO using quantum annealing or classical simulation
        """
        Q = self.construct_qubo_matrix()
        
        if use_quantum and QUANTUM_AVAILABLE:
            return self._solve_quantum(Q, num_reads)
        else:
            return self._solve_classical(Q)
    
    def _solve_quantum(self, Q, num_reads):
        """Solve using D-Wave quantum annealer"""
        # Convert to BQM
        bqm = BinaryQuadraticModel.from_numpy_matrix(Q)
        
        # Use D-Wave sampler
        sampler = EmbeddingComposite(DWaveSampler())
        response = sampler.sample(bqm, num_reads=num_reads)
        
        return response.first.sample, response.first.energy
    
    def _solve_classical(self, Q):
        """Solve using classical simulation (simulated annealing)"""
        if QUANTUM_AVAILABLE:
            try:
                bqm = BinaryQuadraticModel.from_numpy_matrix(Q)
                sampler = ExactSolver()
                response = sampler.sample(bqm)
                return response.first.sample, response.first.energy
            except:
                pass

        # Fallback: Simple simulated annealing
        return self._simulated_annealing(Q)

    def _simulated_annealing(self, Q, max_iter=1000, initial_temp=100.0):
        """Simple simulated annealing implementation"""
        n_vars = Q.shape[0]

        # Random initial solution
        current_solution = {i: random.randint(0, 1) for i in range(n_vars)}
        current_energy = self._calculate_energy(Q, current_solution)

        best_solution = current_solution.copy()
        best_energy = current_energy

        temperature = initial_temp

        for iteration in range(max_iter):
            # Create neighbor by flipping a random bit
            neighbor = current_solution.copy()
            flip_var = random.randint(0, n_vars - 1)
            neighbor[flip_var] = 1 - neighbor[flip_var]

            neighbor_energy = self._calculate_energy(Q, neighbor)

            # Accept or reject
            if neighbor_energy < current_energy:
                current_solution = neighbor
                current_energy = neighbor_energy

                if current_energy < best_energy:
                    best_solution = current_solution.copy()
                    best_energy = current_energy
            else:
                # Accept with probability based on temperature
                prob = np.exp(-(neighbor_energy - current_energy) / temperature)
                if random.random() < prob:
                    current_solution = neighbor
                    current_energy = neighbor_energy

            # Cool down
            temperature *= 0.995

        return best_solution, best_energy

    def _calculate_energy(self, Q, solution):
        """Calculate QUBO energy for a given solution"""
        energy = 0.0
        for i in range(Q.shape[0]):
            for j in range(Q.shape[1]):
                if Q[i, j] != 0:
                    energy += Q[i, j] * solution.get(i, 0) * solution.get(j, 0)
        return energy
    
    def solution_to_parking(self, solution):
        """Convert QUBO solution back to parking layout"""
        parking = np.full((self.height, self.width), NUM_MUR, dtype=int)
        
        for var_idx, value in solution.items():
            if value == 1:  # Variable is selected
                i, j, k = self._index_to_var(var_idx)
                if k == 0:
                    parking[i, j] = NUM_ROUTE
                elif k == 1:
                    parking[i, j] = NUM_PLACE
                elif k == 2:
                    parking[i, j] = NUM_MUR
        
        # Ensure entrance and exit are roads
        for (i, j) in COORDS_ENTREES:
            parking[i, j] = NUM_ROUTE
        for (i, j) in COORDS_SORTIES:
            parking[i, j] = NUM_ROUTE
        
        return parking

def optimize_parking_quantum(generations=1, use_quantum=True):
    """
    Main function to optimize parking using quantum annealing
    """
    print("🔬 Initializing Quantum Parking Optimizer...")
    optimizer = QuantumParkingOptimizer()
    
    print(f"📊 QUBO Problem Size:")
    print(f"   Variables: {optimizer.num_vars}")
    print(f"   Matrix: {optimizer.num_vars}×{optimizer.num_vars}")
    print(f"   Candidate Paths: {len(optimizer.candidate_paths)}")
    
    print("⚛️ Solving QUBO...")
    solution, energy = optimizer.solve_qubo(use_quantum=use_quantum)
    
    print("🏗️ Converting solution to parking layout...")
    parking_layout = optimizer.solution_to_parking(solution)
    
    print(f"✅ Optimization complete!")
    print(f"   Energy: {energy:.2f}")
    print(f"   Method: {'Quantum' if use_quantum and QUANTUM_AVAILABLE else 'Classical'}")
    
    return parking_layout, energy, optimizer

if __name__ == "__main__":
    # Test the quantum optimizer
    parking, energy, optimizer = optimize_parking_quantum(use_quantum=False)
    
    print("\nOptimized Parking Layout:")
    print(parking)
    
    # Calculate score using existing scoring function
    from fonctions_algogen import score
    parking_score = score(parking)
    print(f"Traditional Score: {parking_score:.2f}")
