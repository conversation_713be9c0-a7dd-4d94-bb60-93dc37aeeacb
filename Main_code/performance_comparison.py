# -*- coding: utf-8 -*-
"""
Performance Comparison: Genetic Algorithm vs Quantum Annealing QUBO
Based on research insights from PCB routing optimization
"""

import numpy as np
import time
from constantes import *
from fonctions import *
from fonctions_algogen import *
from quantum_parking_optimizer import optimize_parking_quantum, QuantumParkingOptimizer
from parking_visualizer import ParkingVisualizer
import matplotlib.pyplot as plt

def run_genetic_algorithm_benchmark(num_runs=5):
    """Run genetic algorithm multiple times for benchmarking"""
    print("🧬 Running Genetic Algorithm Benchmark...")
    
    results = {
        'scores': [],
        'times': [],
        'generations_to_convergence': [],
        'final_parkings': []
    }
    
    for run in range(num_runs):
        print(f"   Run {run + 1}/{num_runs}...")
        
        start_time = time.time()
        
        # Run genetic algorithm with tracking
        evolParkings, derniereGenxScores, evolScores = evolutionGenetique()
        
        end_time = time.time()
        
        # Extract results
        best_parking = derniereGenxScores[0][0]
        best_score = derniereGenxScores[0][1]
        runtime = end_time - start_time
        
        # Find convergence generation (when improvement becomes minimal)
        convergence_gen = len(evolScores)
        for i in range(10, len(evolScores)):
            if i > 50:  # Look after initial 50 generations
                recent_improvement = max(evolScores[i-10:i]) - min(evolScores[i-10:i])
                if recent_improvement < 1.0:  # Less than 1 point improvement in 10 generations
                    convergence_gen = i
                    break
        
        results['scores'].append(best_score)
        results['times'].append(runtime)
        results['generations_to_convergence'].append(convergence_gen)
        results['final_parkings'].append(best_parking)
    
    return results

def run_quantum_benchmark(num_runs=5):
    """Run quantum annealing multiple times for benchmarking"""
    print("⚛️ Running Quantum Annealing Benchmark...")
    
    results = {
        'scores': [],
        'times': [],
        'energies': [],
        'final_parkings': []
    }
    
    for run in range(num_runs):
        print(f"   Run {run + 1}/{num_runs}...")
        
        start_time = time.time()
        
        # Run quantum optimization
        quantum_parking, quantum_energy, optimizer = optimize_parking_quantum(use_quantum=False)
        quantum_score = score(quantum_parking)
        
        end_time = time.time()
        
        runtime = end_time - start_time
        
        results['scores'].append(quantum_score)
        results['times'].append(runtime)
        results['energies'].append(quantum_energy)
        results['final_parkings'].append(quantum_parking)
    
    return results

def analyze_results(ga_results, quantum_results):
    """Analyze and compare results from both methods"""
    print("\n📊 === PERFORMANCE ANALYSIS ===")
    
    # Statistical analysis
    ga_stats = {
        'mean_score': np.mean(ga_results['scores']),
        'std_score': np.std(ga_results['scores']),
        'mean_time': np.mean(ga_results['times']),
        'std_time': np.std(ga_results['times']),
        'mean_convergence': np.mean(ga_results['generations_to_convergence']),
        'best_score': max(ga_results['scores']),
        'worst_score': min(ga_results['scores'])
    }
    
    quantum_stats = {
        'mean_score': np.mean(quantum_results['scores']),
        'std_score': np.std(quantum_results['scores']),
        'mean_time': np.mean(quantum_results['times']),
        'std_time': np.std(quantum_results['times']),
        'mean_energy': np.mean(quantum_results['energies']),
        'best_score': max(quantum_results['scores']),
        'worst_score': min(quantum_results['scores'])
    }
    
    print(f"\n🧬 GENETIC ALGORITHM RESULTS:")
    print(f"   Average Score: {ga_stats['mean_score']:.2f} ± {ga_stats['std_score']:.2f}")
    print(f"   Best Score: {ga_stats['best_score']:.2f}")
    print(f"   Worst Score: {ga_stats['worst_score']:.2f}")
    print(f"   Average Time: {ga_stats['mean_time']:.1f}s ± {ga_stats['std_time']:.1f}s")
    print(f"   Average Convergence: {ga_stats['mean_convergence']:.0f} generations")
    
    print(f"\n⚛️ QUANTUM ANNEALING RESULTS:")
    print(f"   Average Score: {quantum_stats['mean_score']:.2f} ± {quantum_stats['std_score']:.2f}")
    print(f"   Best Score: {quantum_stats['best_score']:.2f}")
    print(f"   Worst Score: {quantum_stats['worst_score']:.2f}")
    print(f"   Average Time: {quantum_stats['mean_time']:.1f}s ± {quantum_stats['std_time']:.1f}s")
    print(f"   Average Energy: {quantum_stats['mean_energy']:.1f}")
    
    # Performance comparison
    print(f"\n⚡ PERFORMANCE COMPARISON:")
    score_improvement = quantum_stats['mean_score'] - ga_stats['mean_score']
    time_improvement = ga_stats['mean_time'] / quantum_stats['mean_time']
    
    print(f"   Score Difference: {score_improvement:+.2f} points")
    print(f"   Speed Improvement: {time_improvement:.1f}x {'faster' if time_improvement > 1 else 'slower'}")
    print(f"   Consistency (GA): {ga_stats['std_score']:.2f} std dev")
    print(f"   Consistency (Quantum): {quantum_stats['std_score']:.2f} std dev")
    
    return ga_stats, quantum_stats

def create_performance_visualizations(ga_results, quantum_results, ga_stats, quantum_stats):
    """Create comprehensive performance comparison visualizations"""
    print("\n🎨 Creating Performance Visualizations...")
    
    # 1. Score Distribution Comparison
    plt.figure(figsize=(15, 10))
    
    # Subplot 1: Score distributions
    plt.subplot(2, 3, 1)
    plt.hist(ga_results['scores'], alpha=0.7, label='Genetic Algorithm', bins=10, color='blue')
    plt.hist(quantum_results['scores'], alpha=0.7, label='Quantum Annealing', bins=10, color='red')
    plt.xlabel('Score')
    plt.ylabel('Frequency')
    plt.title('Score Distribution Comparison')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Subplot 2: Runtime comparison
    plt.subplot(2, 3, 2)
    methods = ['Genetic\nAlgorithm', 'Quantum\nAnnealing']
    times = [ga_stats['mean_time'], quantum_stats['mean_time']]
    errors = [ga_stats['std_time'], quantum_stats['std_time']]
    
    bars = plt.bar(methods, times, yerr=errors, capsize=5, color=['blue', 'red'], alpha=0.7)
    plt.ylabel('Runtime (seconds)')
    plt.title('Average Runtime Comparison')
    plt.grid(True, alpha=0.3)
    
    # Add value labels on bars
    for bar, time_val in zip(bars, times):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + bar.get_height()*0.05,
                f'{time_val:.1f}s', ha='center', va='bottom', fontweight='bold')
    
    # Subplot 3: Score vs Time scatter
    plt.subplot(2, 3, 3)
    plt.scatter(ga_results['times'], ga_results['scores'], alpha=0.7, label='Genetic Algorithm', 
               color='blue', s=100)
    plt.scatter(quantum_results['times'], quantum_results['scores'], alpha=0.7, label='Quantum Annealing',
               color='red', s=100)
    plt.xlabel('Runtime (seconds)')
    plt.ylabel('Score')
    plt.title('Score vs Runtime')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Subplot 4: Box plot comparison
    plt.subplot(2, 3, 4)
    data_to_plot = [ga_results['scores'], quantum_results['scores']]
    box_plot = plt.boxplot(data_to_plot, labels=['Genetic\nAlgorithm', 'Quantum\nAnnealing'],
                          patch_artist=True)
    box_plot['boxes'][0].set_facecolor('blue')
    box_plot['boxes'][1].set_facecolor('red')
    plt.ylabel('Score')
    plt.title('Score Distribution (Box Plot)')
    plt.grid(True, alpha=0.3)
    
    # Subplot 5: Convergence analysis (GA only)
    plt.subplot(2, 3, 5)
    plt.hist(ga_results['generations_to_convergence'], bins=10, alpha=0.7, color='blue')
    plt.xlabel('Generations to Convergence')
    plt.ylabel('Frequency')
    plt.title('GA Convergence Analysis')
    plt.grid(True, alpha=0.3)
    
    # Subplot 6: Performance radar chart
    plt.subplot(2, 3, 6)
    categories = ['Score\n(normalized)', 'Speed\n(inverse time)', 'Consistency\n(inverse std)']
    
    # Normalize metrics for radar chart
    max_score = max(ga_stats['mean_score'], quantum_stats['mean_score'])
    max_time = max(ga_stats['mean_time'], quantum_stats['mean_time'])
    max_std = max(ga_stats['std_score'], quantum_stats['std_score'])
    
    ga_values = [
        ga_stats['mean_score'] / max_score,
        (max_time - ga_stats['mean_time']) / max_time,  # Inverse time (higher is better)
        (max_std - ga_stats['std_score']) / max_std     # Inverse std (higher is better)
    ]
    
    quantum_values = [
        quantum_stats['mean_score'] / max_score,
        (max_time - quantum_stats['mean_time']) / max_time,
        (max_std - quantum_stats['std_score']) / max_std
    ]
    
    x = np.arange(len(categories))
    width = 0.35
    
    plt.bar(x - width/2, ga_values, width, label='Genetic Algorithm', alpha=0.7, color='blue')
    plt.bar(x + width/2, quantum_values, width, label='Quantum Annealing', alpha=0.7, color='red')
    
    plt.xlabel('Performance Metrics')
    plt.ylabel('Normalized Score (0-1)')
    plt.title('Overall Performance Comparison')
    plt.xticks(x, categories)
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('performance_comparison_comprehensive.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 2. Best solutions comparison
    best_ga_idx = np.argmax(ga_results['scores'])
    best_quantum_idx = np.argmax(quantum_results['scores'])
    
    best_ga_parking = ga_results['final_parkings'][best_ga_idx]
    best_quantum_parking = quantum_results['final_parkings'][best_quantum_idx]
    
    visualizer = ParkingVisualizer()
    visualizer.plot_comparison(
        best_ga_parking,
        best_quantum_parking,
        ga_results['scores'][best_ga_idx],
        quantum_results['scores'][best_quantum_idx],
        save_path="best_solutions_comparison.png",
        show_stats=True
    )
    
    print("✅ Performance visualizations saved:")
    print("   📊 performance_comparison_comprehensive.png")
    print("   🏆 best_solutions_comparison.png")

def main():
    """Run comprehensive performance comparison"""
    print("🔬 === COMPREHENSIVE PERFORMANCE COMPARISON ===")
    print("Genetic Algorithm vs Quantum Annealing QUBO")
    
    num_runs = int(input("Enter number of runs for each method (recommended: 5): ") or "5")
    
    # Run benchmarks
    ga_results = run_genetic_algorithm_benchmark(num_runs)
    quantum_results = run_quantum_benchmark(num_runs)
    
    # Analyze results
    ga_stats, quantum_stats = analyze_results(ga_results, quantum_results)
    
    # Create visualizations
    create_performance_visualizations(ga_results, quantum_results, ga_stats, quantum_stats)
    
    # Summary recommendations
    print(f"\n🎯 === RECOMMENDATIONS ===")
    
    if quantum_stats['mean_score'] > ga_stats['mean_score']:
        print("✅ Quantum Annealing shows better solution quality")
    else:
        print("✅ Genetic Algorithm shows better solution quality")
    
    if quantum_stats['mean_time'] < ga_stats['mean_time']:
        print("⚡ Quantum Annealing is faster")
    else:
        print("⚡ Genetic Algorithm is faster")
    
    if quantum_stats['std_score'] < ga_stats['std_score']:
        print("🎯 Quantum Annealing is more consistent")
    else:
        print("🎯 Genetic Algorithm is more consistent")
    
    print(f"\n🔬 Performance comparison complete!")

if __name__ == "__main__":
    main()
