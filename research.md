# [cite_start]アニーリングマシンを活用した次世代自動配線ツールの開発 [cite: 1]
- [cite_start]電子機器設計の大幅な効率化を目指して - [cite: 1]

---

### [cite_start]1. 背景 [cite: 2]

[cite_start]社会の情報化が加速する中で、その基盤となる電子機器の小型化および集積化が求められています。 [cite: 3] [cite_start]電子機器の根幹をなすプリント基板上には複数の部品が配置され、それらを電気的に接続するための配線が基板表面に敷かれます。 [cite: 3]

[cite_start]配線の長さやビアの数、角度に代表される配線形状は、電気的な特性を左右する支配的な要素です。 [cite: 4] [cite_start]配線時に電気的な特性を考慮しなければノイズが発生しやすくなり、電子機器の動作に支障をきたします。 [cite: 4] [cite_start]一般的なプリント基板は2層以上で構成されるため、配線は三次元的な経路で設計されます。 [cite: 4] [cite_start]膨大な数の配線を三次元的にどのように接続すれば、電気的特性を維持しつつ交差を避けられるかは複雑な組合せ最適化問題であり、効率的に解くことが難しいとされてきました。 [cite: 4]

[cite_start]配線の難易度はプリント基板の大規模化に伴って高くなり、設計に要する時間も長くなります。 [cite: 5] [cite_start]拡大し続ける電子機器の需要に対応するためには、設計工程の大幅な効率化が求められます。 [cite: 5]

[cite_start]**【配線前】** [cite: 6]
**(画像)**
[cite_start]**【配線後】** [cite: 7]
**(画像)**

* [cite_start]上層の配線 [cite: 8]
* [cite_start]下層の配線 [cite: 11]
* [cite_start]ピア（層間の貫通穴） [cite: 9, 10]

[cite_start]**図1 プリント基板の概略図** [cite: 13]

---

### [cite_start]2. 目的 [cite: 12]

[cite_start]本プロジェクトではアニーリングマシンを活用した自動配線アルゴリズムを開発することで配線問題の根本的な解決を目指します。 [cite: 14] [cite_start]これにより、プリント基板の設計工程を大幅に効率化し、安定した電子機器のサプライチェーン構築に貢献します。 [cite: 14]

[cite_start]具体的には、グローバルルーティング（配線経路の大まかな割り当てを行う工程）へのアニーリングマシンの活用を検討します。 [cite: 15] [cite_start]この工程は、詳細な配線を行う前の準備段階にあたり、効率的な配線を実現する上で重要なステップです。 [cite: 15]

---

### [cite_start]3. ソフトウェア開発内容 [cite: 16]

[cite_start]本プロジェクトでは、以下のソフトウェア開発を行いました。 [cite: 17]
1.  [cite_start]配線問題の組合せ最適化問題としての定義 [cite: 17]
2.  [cite_start]アニーリングマシンを活用したグローバルルーティング [cite: 17]
3.  [cite_start]大規模な基板に対しても有効なアルゴリズム [cite: 17]

#### [cite_start]1) 配線問題の組合せ最適化問題としての定義 [cite: 18]

[cite_start]各配線に対して複数の候補経路を準備し、アニーリングマシンを用いて最適な候補経路の組合せを選択するアルゴリズムを開発しました。 [cite: 19] [cite_start]経路長とピア数は電気的特性を左右する支配的な要素であり、これらを最小化することが望ましいです。 [cite: 19] [cite_start]また、同じ層での交差を禁止し、配線密度を上限内に収めることが制約条件となります。 [cite: 19] [cite_start]図2に、具体的な定式化手法と、2配線に対して3本ずつ候補経路を用意し最適化を行う際の概念図を示します。 [cite: 21]

[cite_start]**(画像：【配線0】、【配線1】からアニーリングによって【選択された経路】が決定される概念図)** [cite: 31, 32, 33, 48]

[cite_start]**図2 定式化手法とアルゴリズム概要** [cite: 51]

* **コスト/制約:**
    * [cite_start]交差コスト [cite: 23]
    * [cite_start]経路長コスト [cite: 24]
    * [cite_start]ビアコスト [cite: 25]
    * [cite_start]密度制約 [cite: 26]
    * [cite_start]one-hot制約 [cite: 27]

| | 候補0 | 候補1 | 候補2 |
| :--- | :---: | :---: | :---: |
| [cite_start]**配線0** | q00 [cite: 42] [cite_start]| q01 [cite: 36] [cite_start]| q02 [cite: 44] |
| [cite_start]**配線1** | q10 [cite: 38] [cite_start]| q11 [cite: 46] [cite_start]| q12 [cite: 39] |

* [cite_start]M: 配線数, N: 候補数 [cite: 30]

#### [cite_start]2) アニーリングマシンを活用したグローバルルーティング [cite: 52]

[cite_start]グローバルルーティングの目的は配線経路の大まかな割り当てであり、この段階では高い解像度は求められません。 [cite: 53] [cite_start]詳細な配線を行う際にはグリッド上で経路を生成する必要がありますが（図3左）、グローバルルーティングではグラフ上での経路生成を考えます（図3右）。 [cite: 53] [cite_start]このグラフは、基板上のコンポーネント（ピンやパッド）の中心座標に対し、ドロネー三角形分割を適用して生成したものです。 [cite: 53]

[cite_start]**【グリッド】** [cite: 54]
**(画像)**
[cite_start]**【グラフ】** [cite: 55]
**(画像)**
[cite_start]**図3 グリッドとグラフ** [cite: 56]

[cite_start]グラフ上での経路探索にはA*アルゴリズムを使用します。 [cite: 57] [cite_start]A*アルゴリズムは効率的に最短経路を求める手法ですが、多様な経路の生成には適していません。 [cite: 57] [cite_start]そこで、他の配線を障害物として設定したり、通過禁止のエッジを指定したりすることで、条件を変えながら多様な経路を生成します。 [cite: 57]

[cite_start]生成された経路は、三角形間の移動として表現されます。 [cite: 59] [cite_start]三次元的な経路を考慮するために、通過した三角形ごとに属する層を割り当てます。 [cite: 59] [cite_start]隣接する三角形間で層が異なる場合、その間にピアを配置することになります。 [cite: 59]

[cite_start]この手法を微小規模から中規模の基板で実証した結果、全てのケースで交差や制約違反のない実行可能解を得ることができました。 [cite: 60] [cite_start]数百本程度の候補経路で、数十秒でグローバルルーティングを完了できます。 [cite: 60] [cite_start]微小・小規模の基板では最適な解が得られ、中規模の基板でも従来の手法のように局所解に陥ることはなく、新たなアプローチとして有効であることを確認しました。 [cite: 60]

[cite_start]**(画像：【微小規模】、【小規模】、【中規模】基板での実証試験結果)** [cite: 61, 62, 63]
[cite_start]**図4 微小・小・中規模な基板での実証試験結果** [cite: 64]

#### [cite_start]3) 大規模な基板に対しても有効なアルゴリズム [cite: 65]

[cite_start]大規模な基板では、ランダムに候補経路を増やしても実行可能解を得ることは困難でした。 [cite: 66] [cite_start]そこで、アニーリングマシンの選択結果をフィードバックし、候補経路を更新していく手法を開発しました。 [cite: 66] [cite_start]残っている交差や制約違反に対して有効な候補経路を追加していくことで、サイクルを回すにつれて制約違反が減少し、実行可能解が得られることを確認しました（図5）。 [cite: 66] [cite_start]ビット数が増大する問題に対しては、有望でない候補経路を捨てる処理も実装しています。 [cite: 66]

[cite_start]この手法により、配線数が多い大規模な基板においても、求解精度を確保できるビット数の範囲内で実行可能解を得ることができます。 [cite: 67]

[cite_start]**(画像：【大規模】基板での実証試験結果と【コストと制約違反数の推移】のグラフ)** [cite: 69, 77]
[cite_start]**図5 大規模な基板での実証試験結果とコスト・制約違反数の推移** [cite: 85]

---

### [cite_start]4. 新規性・優位性 [cite: 86]

[cite_start]本手法は、各配線に複数の候補経路を用意し、アニーリングマシンで最適な組合せを選択します。 [cite: 87] [cite_start]結果が配線の順番に依存しないため、従来の自動配線ソフトが抱える課題を根本的に解決できる優位性があります。 [cite: 87]

[cite_start]アニーリングマシンの、制約を満たさない場合でも有用な解を得られるという強みを活かし、結果をフィードバックして候補経路の質を向上させることができます。 [cite: 88] [cite_start]これは他の数理最適化ソルバーでは困難であり、本手法の新規性と言えます。 [cite: 88]

---

### [cite_start]5. 期待されるユーザー価値と社会へのインパクト [cite: 89]

[cite_start]ユーザーとしてプリント基板の設計者を想定しています。 [cite: 90] [cite_start]本ソフトウェアにより、設計者は配線にかかる作業時間を大幅に短縮できる可能性があります。 [cite: 90] [cite_start]グローバルルーティングの結果は、配線をどう敷くべきかの指標となり、試行回数の削減に繋がります。 [cite: 90] [cite_start]今後は詳細な配線処理を実装し、製造可能なデータを出力することで、配線工程の完全な自動化を目指します。 [cite: 90]

---

### [cite_start]6. 氏名(所属) [cite: 91]

* [cite_start]加藤駿典 (東北大学工学部電気情報物理工学科) [cite: 92]
* [cite_start]永山虹空 (東北大学工学部機械知能・航空工学科) [cite: 92]
* [cite_start]遠山 航汰 (東北大学工学部電気情報物理工学科) [cite: 92]